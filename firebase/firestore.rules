rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Admin Users Collection (for web admin panel)
    match /admin_users/{userId} {
      // Allow read/write only if the user is authenticated and accessing their own document
      allow read, write: if request.auth != null && request.auth.uid == userId;

      // Allow super admins to read/write all admin user documents
      allow read, write: if request.auth != null &&
        exists(/databases/$(database)/documents/admin_users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/admin_users/$(request.auth.uid)).data.role == 'super_admin';
    }

    // Test Collection (for Firebase connection testing)
    match /test-connection/{docId} {
      // Allow authenticated admin users to read/write test documents
      allow read, write: if request.auth != null &&
        exists(/databases/$(database)/documents/admin_users/$(request.auth.uid));
    }

    // Mobile App Users collection
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'master_admin'];
      // Allow admin users to read mobile app user data
      allow read: if request.auth != null &&
        exists(/databases/$(database)/documents/admin_users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/admin_users/$(request.auth.uid)).data.isActive == true;
    }
    
    // Questions collection
    match /questions/{questionId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'master_admin'];
      // Allow admin users to read/write questions
      allow read, write: if request.auth != null &&
        exists(/databases/$(database)/documents/admin_users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/admin_users/$(request.auth.uid)).data.isActive == true;
    }

    // Categories collection
    match /categories/{categoryId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'master_admin'];
      // Allow admin users to read/write categories
      allow read, write: if request.auth != null &&
        exists(/databases/$(database)/documents/admin_users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/admin_users/$(request.auth.uid)).data.isActive == true;
    }
    
    // Quiz sessions collection
    match /quiz_sessions/{sessionId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
      allow read: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'master_admin'];
    }
    
    // Quiz results collection
    match /quiz_results/{resultId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
      allow read: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'master_admin'];
    }
    
    // Analytics collection (admin only)
    match /analytics/{analyticsId} {
      allow read, write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'master_admin'];
    }
    
    // Leaderboard collection
    match /leaderboard/{leaderboardId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'master_admin'];
    }
    
    // Daily challenges collection
    match /daily_challenges/{challengeId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'master_admin'];
    }
    
    // User bookmarks
    match /user_bookmarks/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // App settings (admin only)
    match /app_settings/{settingId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'master_admin'];
    }
  }
}
