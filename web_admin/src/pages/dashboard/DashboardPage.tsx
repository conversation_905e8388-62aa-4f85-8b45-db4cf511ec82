import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Paper,
  CardActionArea,
  Avatar,
} from '@mui/material';
import {
  Quiz as QuizIcon,
  Category as CategoryIcon,
  People as PeopleIcon,
  Analytics as AnalyticsIcon,
  CloudUpload as UploadIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import UserManagement from '../../components/admin/UserManagement';

const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { adminUser } = useAuth();

  const handleCardClick = (path: string) => {
    navigate(path);
  };

  // Navigation cards data
  const navigationCards = [
    {
      title: 'Question Management',
      description: 'Create, edit, and manage quiz questions',
      icon: <QuizIcon />,
      path: '/questions',
      color: '#6366F1',
    },
    {
      title: 'Category Management',
      description: 'Organize questions into categories',
      icon: <CategoryIcon />,
      path: '/categories',
      color: '#06B6D4',
    },
    {
      title: 'User Management',
      description: 'View and manage user accounts',
      icon: <PeopleIcon />,
      path: '/users',
      color: '#10B981',
    },
    {
      title: 'Analytics Dashboard',
      description: 'View quiz performance and statistics',
      icon: <AnalyticsIcon />,
      path: '/analytics',
      color: '#F59E0B',
    },
    {
      title: 'Bulk Upload',
      description: 'Upload questions in bulk from files',
      icon: <UploadIcon />,
      path: '/bulk-upload',
      color: '#EF4444',
    },
    {
      title: 'Settings',
      description: 'Configure application settings',
      icon: <SettingsIcon />,
      path: '/settings',
      color: '#8B5CF6',
    },
  ];

  const testCards = [
    {
      title: 'Firebase Connection Test',
      description: 'Test Firebase services and configuration',
      icon: <SettingsIcon />,
      path: '/test-firebase',
      color: '#ff5722',
    },
  ];

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Admin Dashboard
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Welcome to the MCQ Quiz Admin Panel. Select a section below to get started.
      </Typography>

      {/* Navigation Cards */}
      <Typography variant="h5" component="h2" gutterBottom sx={{ mt: 2, mb: 3 }}>
        Quick Access
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        {navigationCards.map((card, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Card
              sx={{
                height: '100%',
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 4,
                },
              }}
            >
              <CardActionArea
                onClick={() => handleCardClick(card.path)}
                sx={{ height: '100%', p: 3 }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar
                    sx={{
                      bgcolor: card.color,
                      mr: 2,
                      width: 56,
                      height: 56,
                    }}
                  >
                    {card.icon}
                  </Avatar>
                  <Box>
                    <Typography variant="h6" component="h3" gutterBottom>
                      {card.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {card.description}
                    </Typography>
                  </Box>
                </Box>
              </CardActionArea>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Test Tools */}
      <Typography variant="h5" component="h2" gutterBottom sx={{ mt: 4, mb: 3 }}>
        Development Tools
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        {testCards.map((card, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Card
              sx={{
                height: '100%',
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 4,
                },
              }}
            >
              <CardActionArea
                onClick={() => handleCardClick(card.path)}
                sx={{ height: '100%', p: 3 }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar
                    sx={{
                      bgcolor: card.color,
                      mr: 2,
                      width: 56,
                      height: 56,
                    }}
                  >
                    {card.icon}
                  </Avatar>
                  <Box>
                    <Typography variant="h6" component="h3" gutterBottom>
                      {card.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {card.description}
                    </Typography>
                  </Box>
                </Box>
              </CardActionArea>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* System Admin Section */}
      {adminUser?.role === 'system_admin' && (
        <>
          <Typography variant="h5" component="h2" gutterBottom sx={{ mt: 4, mb: 3 }}>
            🔧 System Administration
          </Typography>

          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12}>
              <UserManagement />
            </Grid>
          </Grid>
        </>
      )}

      {/* Stats Overview */}
      <Typography variant="h5" component="h2" gutterBottom sx={{ mt: 4, mb: 3 }}>
        Overview
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Questions
              </Typography>
              <Typography variant="h4">
                1,234
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Users
              </Typography>
              <Typography variant="h4">
                567
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Categories
              </Typography>
              <Typography variant="h4">
                12
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Quiz Sessions
              </Typography>
              <Typography variant="h4">
                2,345
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Recent Activity
            </Typography>
            <Typography color="textSecondary">
              Dashboard content will be implemented here...
            </Typography>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardPage;
