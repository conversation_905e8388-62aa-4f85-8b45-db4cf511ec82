# 🔥 Firebase Setup Guide for Web Admin

## 🚨 Current Issue: `auth/network-request-failed`

This error occurs because Firebase is not properly configured with real project credentials.

## 🛠️ Quick Fix Steps

### Step 1: Create/Access Firebase Project

1. **Go to [Firebase Console](https://console.firebase.google.com/)**
2. **Select existing project**: `mcq-quiz-system` (or create new one)
3. **Enable Authentication**:
   - Go to **Authentication** → **Sign-in method**
   - **Enable Google** provider
   - Add your domain to **Authorized domains** (add `localhost` for development)

### Step 2: Get Firebase Configuration

1. In Firebase Console → **Project Settings** (gear icon)
2. Scroll to **"Your apps"** section
3. **Add app** → **Web app** (</>) if not exists
4. **Copy the Firebase config object** (looks like this):

```javascript
const firebaseConfig = {
  apiKey: "AIzaSyDIdFTL8Xl-E02bYB_HnuymfGBRRL6xBqk",
  authDomain: "mcq-quiz-system.firebaseapp.com",
  projectId: "mcq-quiz-system",
  storageBucket: "mcq-quiz-system.firebasestorage.app",
  messagingSenderId: "109048215498",
  appId: "1:109048215498:web:398b38704a2b075fb08133",
  measurementId: "G-F5Z833J800"
};
```

### Step 3: Update Environment Variables

1. **Copy** `.env.local.example` to `.env.local`:
   ```bash
   cp .env.local.example .env.local
   ```

2. **Edit** `.env.local` with your actual Firebase config values:
   ```env
   REACT_APP_FIREBASE_API_KEY=your-actual-api-key
   REACT_APP_FIREBASE_AUTH_DOMAIN=mcq-quiz-system.firebaseapp.com
   REACT_APP_FIREBASE_PROJECT_ID=mcq-quiz-system
   REACT_APP_FIREBASE_STORAGE_BUCKET=mcq-quiz-system.appspot.com
   REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
   REACT_APP_FIREBASE_APP_ID=your-app-id
   REACT_APP_FIREBASE_MEASUREMENT_ID=your-measurement-id
   ```

3. **Restart** your development server:
   ```bash
   npm start
   ```

### Step 4: Test Authentication

1. **Open** http://localhost:3002
2. **Click** "Sign in with Google"
3. **Should work** without network errors

## 🔧 Alternative: Use Firebase Emulators

If you prefer local development without real Firebase:

1. **Install Java** (required for emulators)
2. **Start emulators**:
   ```bash
   cd firebase
   firebase emulators:start
   ```
3. **Update** `.env.local`:
   ```env
   REACT_APP_ENVIRONMENT=development
   ```

## 🎯 Expected Result

After setup, you should see:
- ✅ No network errors
- ✅ Google sign-in popup works
- ✅ Successful authentication
- ✅ Redirect to admin dashboard

## 🆘 Still Having Issues?

Check browser console for detailed error messages and ensure:
- Firebase project exists and is active
- Google authentication is enabled
- Domain is authorized
- Environment variables are correct
- Development server was restarted
