# Firebase Configuration for Web Admin
# Copy this file to .env.local and replace with your actual Firebase config values
# Get these values from: Firebase Console → Project Settings → General → Your apps → Web app

# 🔥 REPLACE THESE WITH YOUR ACTUAL FIREBASE CONFIG VALUES 🔥
REACT_APP_FIREBASE_API_KEY=your-web-api-key-here
REACT_APP_FIREBASE_AUTH_DOMAIN=mcq-quiz-system.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=mcq-quiz-system
REACT_APP_FIREBASE_STORAGE_BUCKET=mcq-quiz-system.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your-messaging-sender-id
REACT_APP_FIREBASE_APP_ID=your-web-app-id
REACT_APP_FIREBASE_MEASUREMENT_ID=your-measurement-id

# Environment
REACT_APP_ENVIRONMENT=development

# API Configuration
REACT_APP_API_URL=http://localhost:5001

# Instructions:
# 1. Go to https://console.firebase.google.com/
# 2. Select your project (mcq-quiz-system)
# 3. Go to Project Settings (gear icon)
# 4. Scroll down to "Your apps" section
# 5. Click on the web app or create a new one
# 6. Copy the config values and replace the placeholders above
# 7. Save this file as .env.local (remove .template)
# 8. Restart your development server
